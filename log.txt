三种计算方法对比测试系统
=================================
=== 三种计算方法综合对比测试 ===
方法1: reference - ac_datatypes (黄金标准)
方法2: c_cal - dcim_macro_com (C语言实现)
方法3: cpp_cal - VectorDotProductInterface (C++实现)
支持数据类型: INT4, INT8, INT16, FP16, BF16, FP8E4, FP8E5

=== 测试 INT4 x INT4 (64 个权重向量) ===
权重 #54 (C/C++不一致):
  参考结果: 0.000000 (原始: 0x0)
  C 结果:   0.500000 (原始: 0x3f000000)
  C++结果:  0.000000 (原始: 0x0)
  误差 C vs 参考:   0.500000 ✗
  误差 C++ vs 参考: 0.000000 ✓
  误差 C vs C++:    0.500000 ✗
  时间 (参考/C/C++): 0.006529ms / 0.002183ms / 0.000623ms


INT4 x INT4 汇总:
  匹配率 (C vs 参考):   63/64 (98.4%)
  匹配率 (C++ vs 参考): 64/64 (100.0%)
  匹配率 (C vs C++):    63/64 (98.4%)
  平均误差 (C vs 参考):   7.812e-03
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    7.812e-03
  最大误差 (C vs 参考):   5.000e-01
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    5.000e-01
  平均时间 (参考):        0.0045ms
  平均时间 (C):           0.0032ms
  平均时间 (C++):         0.0013ms

=== 测试 INT4 x INT8 (32 个权重向量) ===

INT4 x INT8 汇总:
  匹配率 (C vs 参考):   32/32 (100.0%)
  匹配率 (C++ vs 参考): 32/32 (100.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0046ms
  平均时间 (C):           0.0027ms
  平均时间 (C++):         0.0006ms

=== 测试 INT4 x INT16 (16 个权重向量) ===

INT4 x INT16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0027ms
  平均时间 (C):           0.0028ms
  平均时间 (C++):         0.0012ms

=== 测试 INT4 x FP16 (16 个权重向量) ===

INT4 x FP16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   7.708e-01
  平均误差 (C++ vs 参考): 7.708e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.752e+00
  最大误差 (C++ vs 参考): 1.752e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0808ms
  平均时间 (C):           0.0056ms
  平均时间 (C++):         0.0049ms

=== 测试 INT4 x BF16 (16 个权重向量) ===

INT4 x BF16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.274e-01
  平均误差 (C++ vs 参考): 1.274e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   2.773e-01
  最大误差 (C++ vs 参考): 2.773e-01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0647ms
  平均时间 (C):           0.0052ms
  平均时间 (C++):         0.0053ms

=== 测试 INT4 x FP8E4 (32 个权重向量) ===

INT4 x FP8E4 汇总:
  匹配率 (C vs 参考):   4/32 (12.5%)
  匹配率 (C++ vs 参考): 4/32 (12.5%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   1.762e+00
  平均误差 (C++ vs 参考): 1.762e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   4.287e+00
  最大误差 (C++ vs 参考): 4.287e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0443ms
  平均时间 (C):           0.0056ms
  平均时间 (C++):         0.0043ms

=== 测试 INT4 x FP8E5 (32 个权重向量) ===

INT4 x FP8E5 汇总:
  匹配率 (C vs 参考):   1/32 (3.1%)
  匹配率 (C++ vs 参考): 1/32 (3.1%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   3.504e+00
  平均误差 (C++ vs 参考): 3.504e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   9.171e+00
  最大误差 (C++ vs 参考): 9.171e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0343ms
  平均时间 (C):           0.0060ms
  平均时间 (C++):         0.0066ms

=== 测试 INT8 x INT4 (64 个权重向量) ===

INT8 x INT4 汇总:
  匹配率 (C vs 参考):   64/64 (100.0%)
  匹配率 (C++ vs 参考): 64/64 (100.0%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0015ms
  平均时间 (C):           0.0028ms
  平均时间 (C++):         0.0006ms

=== 测试 INT8 x INT8 (32 个权重向量) ===

INT8 x INT8 汇总:
  匹配率 (C vs 参考):   32/32 (100.0%)
  匹配率 (C++ vs 参考): 32/32 (100.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0017ms
  平均时间 (C):           0.0027ms
  平均时间 (C++):         0.0013ms

=== 测试 INT8 x INT16 (16 个权重向量) ===

INT8 x INT16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0012ms
  平均时间 (C):           0.0036ms
  平均时间 (C++):         0.0005ms

=== 测试 INT8 x FP16 (16 个权重向量) ===

INT8 x FP16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   6.994e+00
  平均误差 (C++ vs 参考): 6.994e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   2.484e+01
  最大误差 (C++ vs 参考): 2.484e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0324ms
  平均时间 (C):           0.0053ms
  平均时间 (C++):         0.0053ms

=== 测试 INT8 x BF16 (16 个权重向量) ===

INT8 x BF16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.507e+00
  平均误差 (C++ vs 参考): 1.507e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   5.707e+00
  最大误差 (C++ vs 参考): 5.707e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0283ms
  平均时间 (C):           0.0051ms
  平均时间 (C++):         0.0042ms

=== 测试 INT8 x FP8E4 (32 个权重向量) ===

INT8 x FP8E4 汇总:
  匹配率 (C vs 参考):   2/32 (6.2%)
  匹配率 (C++ vs 参考): 2/32 (6.2%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   3.784e+01
  平均误差 (C++ vs 参考): 3.784e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.137e+02
  最大误差 (C++ vs 参考): 1.137e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0298ms
  平均时间 (C):           0.0052ms
  平均时间 (C++):         0.0057ms

=== 测试 INT8 x FP8E5 (32 个权重向量) ===

INT8 x FP8E5 汇总:
  匹配率 (C vs 参考):   2/32 (6.2%)
  匹配率 (C++ vs 参考): 2/32 (6.2%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   5.549e+01
  平均误差 (C++ vs 参考): 5.549e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.841e+02
  最大误差 (C++ vs 参考): 1.841e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0295ms
  平均时间 (C):           0.0053ms
  平均时间 (C++):         0.0042ms

=== 测试 INT16 x INT4 (64 个权重向量) ===

INT16 x INT4 汇总:
  匹配率 (C vs 参考):   64/64 (100.0%)
  匹配率 (C++ vs 参考): 64/64 (100.0%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0012ms
  平均时间 (C):           0.0026ms
  平均时间 (C++):         0.0012ms

=== 测试 INT16 x INT8 (32 个权重向量) ===

INT16 x INT8 汇总:
  匹配率 (C vs 参考):   32/32 (100.0%)
  匹配率 (C++ vs 参考): 32/32 (100.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   0.000e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   0.000e+00
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0020ms
  平均时间 (C):           0.0022ms
  平均时间 (C++):         0.0005ms

=== 测试 INT16 x INT16 (16 个权重向量) ===

INT16 x INT16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   8.625e+00
  平均误差 (C++ vs 参考): 0.000e+00
  平均误差 (C vs C++):    8.625e+00
  最大误差 (C vs 参考):   3.200e+01
  最大误差 (C++ vs 参考): 0.000e+00
  最大误差 (C vs C++):    3.200e+01
  平均时间 (参考):        0.0009ms
  平均时间 (C):           0.0022ms
  平均时间 (C++):         0.0013ms

=== 测试 INT16 x FP16 (16 个权重向量) ===

INT16 x FP16 汇总:
  匹配率 (C vs 参考):   15/16 (93.8%)
  匹配率 (C++ vs 参考): 15/16 (93.8%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   2.759e+02
  平均误差 (C++ vs 参考): 2.757e+02
  平均误差 (C vs C++):    2.578e-01
  最大误差 (C vs 参考):   6.368e+02
  最大误差 (C++ vs 参考): 6.368e+02
  最大误差 (C vs C++):    1.000e+00
  平均时间 (参考):        0.0275ms
  平均时间 (C):           0.0051ms
  平均时间 (C++):         0.0043ms

=== 测试 INT16 x BF16 (16 个权重向量) ===

INT16 x BF16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   6.421e+01
  平均误差 (C++ vs 参考): 6.421e+01
  平均误差 (C vs C++):    5.469e-02
  最大误差 (C vs 参考):   1.318e+02
  最大误差 (C++ vs 参考): 1.318e+02
  最大误差 (C vs C++):    2.500e-01
  平均时间 (参考):        0.0279ms
  平均时间 (C):           0.0047ms
  平均时间 (C++):         0.0050ms

=== 测试 INT16 x FP8E4 (32 个权重向量) ===

INT16 x FP8E4 汇总:
  匹配率 (C vs 参考):   0/32 (0.0%)
  匹配率 (C++ vs 参考): 0/32 (0.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   3.733e+03
  平均误差 (C++ vs 参考): 3.733e+03
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   6.127e+03
  最大误差 (C++ vs 参考): 6.127e+03
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0291ms
  平均时间 (C):           0.0049ms
  平均时间 (C++):         0.0041ms

=== 测试 INT16 x FP8E5 (32 个权重向量) ===

INT16 x FP8E5 汇总:
  匹配率 (C vs 参考):   0/32 (0.0%)
  匹配率 (C++ vs 参考): 0/32 (0.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   7.862e+03
  平均误差 (C++ vs 参考): 7.862e+03
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.342e+04
  最大误差 (C++ vs 参考): 1.342e+04
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0280ms
  平均时间 (C):           0.0056ms
  平均时间 (C++):         0.0049ms

=== 测试 FP16 x INT4 (64 个权重向量) ===

FP16 x INT4 汇总:
  匹配率 (C vs 参考):   61/64 (95.3%)
  匹配率 (C++ vs 参考): 61/64 (95.3%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   6.473e-01
  平均误差 (C++ vs 参考): 6.473e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.707e+00
  最大误差 (C++ vs 参考): 1.707e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0274ms
  平均时间 (C):           0.0052ms
  平均时间 (C++):         0.0038ms

=== 测试 FP16 x INT8 (32 个权重向量) ===

FP16 x INT8 汇总:
  匹配率 (C vs 参考):   30/32 (93.8%)
  匹配率 (C++ vs 参考): 30/32 (93.8%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   9.729e+00
  平均误差 (C++ vs 参考): 9.729e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   2.625e+01
  最大误差 (C++ vs 参考): 2.625e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0275ms
  平均时间 (C):           0.0051ms
  平均时间 (C++):         0.0046ms

=== 测试 FP16 x INT16 (16 个权重向量) ===

FP16 x INT16 汇总:
  匹配率 (C vs 参考):   16/16 (100.0%)
  匹配率 (C++ vs 参考): 16/16 (100.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.678e+03
  平均误差 (C++ vs 参考): 1.677e+03
  平均误差 (C vs C++):    1.188e+00
  最大误差 (C vs 参考):   4.088e+03
  最大误差 (C++ vs 参考): 4.088e+03
  最大误差 (C vs C++):    8.000e+00
  平均时间 (参考):        0.0259ms
  平均时间 (C):           0.0047ms
  平均时间 (C++):         0.0036ms

=== 测试 FP16 x FP16 (16 个权重向量) ===

FP16 x FP16 汇总:
  匹配率 (C vs 参考):   15/16 (93.8%)
  匹配率 (C++ vs 参考): 15/16 (93.8%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.083e+02
  平均误差 (C++ vs 参考): 1.083e+02
  平均误差 (C vs C++):    3.711e-02
  最大误差 (C vs 参考):   2.610e+02
  最大误差 (C++ vs 参考): 2.610e+02
  最大误差 (C vs C++):    1.250e-01
  平均时间 (参考):        0.0505ms
  平均时间 (C):           0.0072ms
  平均时间 (C++):         0.0085ms

=== 测试 FP16 x BF16 (16 个权重向量) ===

FP16 x BF16 汇总:
  匹配率 (C vs 参考):   13/16 (81.2%)
  匹配率 (C++ vs 参考): 13/16 (81.2%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   2.284e+01
  平均误差 (C++ vs 参考): 2.284e+01
  平均误差 (C vs C++):    9.033e-03
  最大误差 (C vs 参考):   5.616e+01
  最大误差 (C++ vs 参考): 5.616e+01
  最大误差 (C vs C++):    3.125e-02
  平均时间 (参考):        0.0558ms
  平均时间 (C):           0.0077ms
  平均时间 (C++):         0.0087ms

=== 测试 FP16 x FP8E4 (32 个权重向量) ===

FP16 x FP8E4 汇总:
  匹配率 (C vs 参考):   2/32 (6.2%)
  匹配率 (C++ vs 参考): 2/32 (6.2%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   2.967e+02
  平均误差 (C++ vs 参考): 2.967e+02
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   7.182e+02
  最大误差 (C++ vs 参考): 7.182e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0575ms
  平均时间 (C):           0.0078ms
  平均时间 (C++):         0.0084ms

=== 测试 FP16 x FP8E5 (32 个权重向量) ===

FP16 x FP8E5 汇总:
  匹配率 (C vs 参考):   0/32 (0.0%)
  匹配率 (C++ vs 参考): 0/32 (0.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   5.832e+02
  平均误差 (C++ vs 参考): 5.832e+02
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.607e+03
  最大误差 (C++ vs 参考): 1.607e+03
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0646ms
  平均时间 (C):           0.0078ms
  平均时间 (C++):         0.0075ms

=== 测试 BF16 x INT4 (64 个权重向量) ===

BF16 x INT4 汇总:
  匹配率 (C vs 参考):   63/64 (98.4%)
  匹配率 (C++ vs 参考): 63/64 (98.4%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   1.313e-01
  平均误差 (C++ vs 参考): 1.313e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   4.584e-01
  最大误差 (C++ vs 参考): 4.584e-01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0290ms
  平均时间 (C):           0.0052ms
  平均时间 (C++):         0.0048ms

=== 测试 BF16 x INT8 (32 个权重向量) ===

BF16 x INT8 汇总:
  匹配率 (C vs 参考):   32/32 (100.0%)
  匹配率 (C++ vs 参考): 32/32 (100.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   2.618e+00
  平均误差 (C++ vs 参考): 2.618e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   8.555e+00
  最大误差 (C++ vs 参考): 8.555e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0316ms
  平均时间 (C):           0.0047ms
  平均时间 (C++):         0.0036ms

=== 测试 BF16 x INT16 (16 个权重向量) ===

BF16 x INT16 汇总:
  匹配率 (C vs 参考):   15/16 (93.8%)
  匹配率 (C++ vs 参考): 15/16 (93.8%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   6.277e+02
  平均误差 (C++ vs 参考): 6.279e+02
  平均误差 (C vs C++):    5.000e-01
  最大误差 (C vs 参考):   1.728e+03
  最大误差 (C++ vs 参考): 1.728e+03
  最大误差 (C vs C++):    2.000e+00
  平均时间 (参考):        0.0273ms
  平均时间 (C):           0.0047ms
  平均时间 (C++):         0.0046ms

=== 测试 BF16 x FP16 (16 个权重向量) ===

BF16 x FP16 汇总:
  匹配率 (C vs 参考):   15/16 (93.8%)
  匹配率 (C++ vs 参考): 15/16 (93.8%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.410e+01
  平均误差 (C++ vs 参考): 1.411e+01
  平均误差 (C vs C++):    8.057e-03
  最大误差 (C vs 参考):   2.632e+01
  最大误差 (C++ vs 参考): 2.631e+01
  最大误差 (C vs C++):    6.250e-02
  平均时间 (参考):        0.0540ms
  平均时间 (C):           0.0071ms
  平均时间 (C++):         0.0074ms

=== 测试 BF16 x BF16 (16 个权重向量) ===

BF16 x BF16 汇总:
  匹配率 (C vs 参考):   14/16 (87.5%)
  匹配率 (C++ vs 参考): 14/16 (87.5%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   3.508e+00
  平均误差 (C++ vs 参考): 3.509e+00
  平均误差 (C vs C++):    4.333e-03
  最大误差 (C vs 参考):   1.061e+01
  最大误差 (C++ vs 参考): 1.061e+01
  最大误差 (C vs C++):    1.562e-02
  平均时间 (参考):        0.0553ms
  平均时间 (C):           0.0072ms
  平均时间 (C++):         0.0082ms

=== 测试 BF16 x FP8E4 (32 个权重向量) ===

BF16 x FP8E4 汇总:
  匹配率 (C vs 参考):   2/32 (6.2%)
  匹配率 (C++ vs 参考): 2/32 (6.2%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   5.564e+01
  平均误差 (C++ vs 参考): 5.564e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.536e+02
  最大误差 (C++ vs 参考): 1.536e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0585ms
  平均时间 (C):           0.0077ms
  平均时间 (C++):         0.0076ms

=== 测试 BF16 x FP8E5 (32 个权重向量) ===

BF16 x FP8E5 汇总:
  匹配率 (C vs 参考):   1/32 (3.1%)
  匹配率 (C++ vs 参考): 1/32 (3.1%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   1.264e+02
  平均误差 (C++ vs 参考): 1.264e+02
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   4.311e+02
  最大误差 (C++ vs 参考): 4.311e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.1168ms
  平均时间 (C):           0.0101ms
  平均时间 (C++):         0.0099ms

=== 测试 FP8E4 x INT4 (64 个权重向量) ===

FP8E4 x INT4 汇总:
  匹配率 (C vs 参考):   3/64 (4.7%)
  匹配率 (C++ vs 参考): 3/64 (4.7%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   2.190e+00
  平均误差 (C++ vs 参考): 2.190e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   6.266e+00
  最大误差 (C++ vs 参考): 6.266e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0336ms
  平均时间 (C):           0.0050ms
  平均时间 (C++):         0.0045ms

=== 测试 FP8E4 x INT8 (32 个权重向量) ===

FP8E4 x INT8 汇总:
  匹配率 (C vs 参考):   4/32 (12.5%)
  匹配率 (C++ vs 参考): 4/32 (12.5%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   3.398e+01
  平均误差 (C++ vs 参考): 3.398e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.116e+02
  最大误差 (C++ vs 参考): 1.116e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0290ms
  平均时间 (C):           0.0042ms
  平均时间 (C++):         0.0038ms

=== 测试 FP8E4 x INT16 (16 个权重向量) ===

FP8E4 x INT16 汇总:
  匹配率 (C vs 参考):   0/16 (0.0%)
  匹配率 (C++ vs 参考): 0/16 (0.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   6.843e+03
  平均误差 (C++ vs 参考): 6.843e+03
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.909e+04
  最大误差 (C++ vs 参考): 1.909e+04
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0297ms
  平均时间 (C):           0.0044ms
  平均时间 (C++):         0.0047ms

=== 测试 FP8E4 x FP16 (16 个权重向量) ===

FP8E4 x FP16 汇总:
  匹配率 (C vs 参考):   2/16 (12.5%)
  匹配率 (C++ vs 参考): 2/16 (12.5%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   2.202e+02
  平均误差 (C++ vs 参考): 2.202e+02
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   9.499e+02
  最大误差 (C++ vs 参考): 9.499e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0562ms
  平均时间 (C):           0.0068ms
  平均时间 (C++):         0.0079ms

=== 测试 FP8E4 x BF16 (16 个权重向量) ===

FP8E4 x BF16 汇总:
  匹配率 (C vs 参考):   0/16 (0.0%)
  匹配率 (C++ vs 参考): 0/16 (0.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   5.822e+01
  平均误差 (C++ vs 参考): 5.822e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.362e+02
  最大误差 (C++ vs 参考): 1.362e+02
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0557ms
  平均时间 (C):           0.0076ms
  平均时间 (C++):         0.0077ms

=== 测试 FP8E4 x FP8E4 (32 个权重向量) ===

FP8E4 x FP8E4 汇总:
  匹配率 (C vs 参考):   2/32 (6.2%)
  匹配率 (C++ vs 参考): 2/32 (6.2%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   6.002e+00
  平均误差 (C++ vs 参考): 6.002e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.382e+01
  最大误差 (C++ vs 参考): 1.382e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0571ms
  平均时间 (C):           0.0067ms
  平均时间 (C++):         0.0077ms

=== 测试 FP8E4 x FP8E5 (32 个权重向量) ===

FP8E4 x FP8E5 汇总:
  匹配率 (C vs 参考):   1/32 (3.1%)
  匹配率 (C++ vs 参考): 1/32 (3.1%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   1.238e+01
  平均误差 (C++ vs 参考): 1.238e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   3.259e+01
  最大误差 (C++ vs 参考): 3.259e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0595ms
  平均时间 (C):           0.0069ms
  平均时间 (C++):         0.0081ms

=== 测试 FP8E5 x INT4 (64 个权重向量) ===

FP8E5 x INT4 汇总:
  匹配率 (C vs 参考):   3/64 (4.7%)
  匹配率 (C++ vs 参考): 3/64 (4.7%)
  匹配率 (C vs C++):    64/64 (100.0%)
  平均误差 (C vs 参考):   1.644e-01
  平均误差 (C++ vs 参考): 1.644e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   4.767e-01
  最大误差 (C++ vs 参考): 4.767e-01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0294ms
  平均时间 (C):           0.0046ms
  平均时间 (C++):         0.0040ms

=== 测试 FP8E5 x INT8 (32 个权重向量) ===

FP8E5 x INT8 汇总:
  匹配率 (C vs 参考):   0/32 (0.0%)
  匹配率 (C++ vs 参考): 0/32 (0.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   2.656e+00
  平均误差 (C++ vs 参考): 2.656e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   5.802e+00
  最大误差 (C++ vs 参考): 5.802e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0296ms
  平均时间 (C):           0.0041ms
  平均时间 (C++):         0.0041ms

=== 测试 FP8E5 x INT16 (16 个权重向量) ===

FP8E5 x INT16 汇总:
  匹配率 (C vs 参考):   1/16 (6.2%)
  匹配率 (C++ vs 参考): 1/16 (6.2%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   5.066e+02
  平均误差 (C++ vs 参考): 5.066e+02
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.333e+03
  最大误差 (C++ vs 参考): 1.333e+03
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0324ms
  平均时间 (C):           0.0042ms
  平均时间 (C++):         0.0039ms

=== 测试 FP8E5 x FP16 (16 个权重向量) ===

FP8E5 x FP16 汇总:
  匹配率 (C vs 参考):   0/16 (0.0%)
  匹配率 (C++ vs 参考): 0/16 (0.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   1.783e+01
  平均误差 (C++ vs 参考): 1.783e+01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   4.606e+01
  最大误差 (C++ vs 参考): 4.606e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0599ms
  平均时间 (C):           0.0066ms
  平均时间 (C++):         0.0080ms

=== 测试 FP8E5 x BF16 (16 个权重向量) ===

FP8E5 x BF16 汇总:
  匹配率 (C vs 参考):   0/16 (0.0%)
  匹配率 (C++ vs 参考): 0/16 (0.0%)
  匹配率 (C vs C++):    16/16 (100.0%)
  平均误差 (C vs 参考):   3.219e+00
  平均误差 (C++ vs 参考): 3.219e+00
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.780e+01
  最大误差 (C++ vs 参考): 1.780e+01
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0628ms
  平均时间 (C):           0.0069ms
  平均时间 (C++):         0.0093ms

=== 测试 FP8E5 x FP8E4 (32 个权重向量) ===

FP8E5 x FP8E4 汇总:
  匹配率 (C vs 参考):   0/32 (0.0%)
  匹配率 (C++ vs 参考): 0/32 (0.0%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   4.434e-01
  平均误差 (C++ vs 参考): 4.434e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.158e+00
  最大误差 (C++ vs 参考): 1.158e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0574ms
  平均时间 (C):           0.0065ms
  平均时间 (C++):         0.0075ms

=== 测试 FP8E5 x FP8E5 (32 个权重向量) ===

FP8E5 x FP8E5 汇总:
  匹配率 (C vs 参考):   1/32 (3.1%)
  匹配率 (C++ vs 参考): 1/32 (3.1%)
  匹配率 (C vs C++):    32/32 (100.0%)
  平均误差 (C vs 参考):   5.556e-01
  平均误差 (C++ vs 参考): 5.556e-01
  平均误差 (C vs C++):    0.000e+00
  最大误差 (C vs 参考):   1.483e+00
  最大误差 (C++ vs 参考): 1.483e+00
  最大误差 (C vs C++):    0.000e+00
  平均时间 (参考):        0.0535ms
  平均时间 (C):           0.0066ms
  平均时间 (C++):         0.0074ms


=== 最终统计报告 ===

按类型组合统计:
类型组合        测试数C匹配率%C++匹配率%C⬌C++匹配%C时间(ms)C++时间(ms)
------------------------------------------------------------------------------------------------
INT4 x INT4               64      98.4%      100.0%       98.4%    0.0032      0.0013
INT4 x INT8               32     100.0%      100.0%      100.0%    0.0027      0.0006
INT4 x INT16              16     100.0%      100.0%      100.0%    0.0028      0.0012
INT4 x FP16               16     100.0%      100.0%      100.0%    0.0056      0.0049
INT4 x BF16               16     100.0%      100.0%      100.0%    0.0052      0.0053
INT4 x FP8E4              32      12.5%       12.5%      100.0%    0.0056      0.0043
INT4 x FP8E5              32       3.1%        3.1%      100.0%    0.0060      0.0066
INT8 x INT4               64     100.0%      100.0%      100.0%    0.0028      0.0006
INT8 x INT8               32     100.0%      100.0%      100.0%    0.0027      0.0013
INT8 x INT16              16     100.0%      100.0%      100.0%    0.0036      0.0005
INT8 x FP16               16     100.0%      100.0%      100.0%    0.0053      0.0053
INT8 x BF16               16     100.0%      100.0%      100.0%    0.0051      0.0042
INT8 x FP8E4              32       6.2%        6.2%      100.0%    0.0052      0.0057
INT8 x FP8E5              32       6.2%        6.2%      100.0%    0.0053      0.0042
INT16 x INT4              64     100.0%      100.0%      100.0%    0.0026      0.0012
INT16 x INT8              32     100.0%      100.0%      100.0%    0.0022      0.0005
INT16 x INT16             16     100.0%      100.0%      100.0%    0.0022      0.0013
INT16 x FP16              16      93.8%       93.8%      100.0%    0.0051      0.0043
INT16 x BF16              16     100.0%      100.0%      100.0%    0.0047      0.0050
INT16 x FP8E4             32       0.0%        0.0%      100.0%    0.0049      0.0041
INT16 x FP8E5             32       0.0%        0.0%      100.0%    0.0056      0.0049
FP16 x INT4               64      95.3%       95.3%      100.0%    0.0052      0.0038
FP16 x INT8               32      93.8%       93.8%      100.0%    0.0051      0.0046
FP16 x INT16              16     100.0%      100.0%      100.0%    0.0047      0.0036
FP16 x FP16               16      93.8%       93.8%      100.0%    0.0072      0.0085
FP16 x BF16               16      81.2%       81.2%      100.0%    0.0077      0.0087
FP16 x FP8E4              32       6.2%        6.2%      100.0%    0.0078      0.0084
FP16 x FP8E5              32       0.0%        0.0%      100.0%    0.0078      0.0075
BF16 x INT4               64      98.4%       98.4%      100.0%    0.0052      0.0048
BF16 x INT8               32     100.0%      100.0%      100.0%    0.0047      0.0036
BF16 x INT16              16      93.8%       93.8%      100.0%    0.0047      0.0046
BF16 x FP16               16      93.8%       93.8%      100.0%    0.0071      0.0074
BF16 x BF16               16      87.5%       87.5%      100.0%    0.0072      0.0082
BF16 x FP8E4              32       6.2%        6.2%      100.0%    0.0077      0.0076
BF16 x FP8E5              32       3.1%        3.1%      100.0%    0.0101      0.0099
FP8E4 x INT4              64       4.7%        4.7%      100.0%    0.0050      0.0045
FP8E4 x INT8              32      12.5%       12.5%      100.0%    0.0042      0.0038
FP8E4 x INT16             16       0.0%        0.0%      100.0%    0.0044      0.0047
FP8E4 x FP16              16      12.5%       12.5%      100.0%    0.0068      0.0079
FP8E4 x BF16              16       0.0%        0.0%      100.0%    0.0076      0.0077
FP8E4 x FP8E4             32       6.2%        6.2%      100.0%    0.0067      0.0077
FP8E4 x FP8E5             32       3.1%        3.1%      100.0%    0.0069      0.0081
FP8E5 x INT4              64       4.7%        4.7%      100.0%    0.0046      0.0040
FP8E5 x INT8              32       0.0%        0.0%      100.0%    0.0041      0.0041
FP8E5 x INT16             16       6.2%        6.2%      100.0%    0.0042      0.0039
FP8E5 x FP16              16       0.0%        0.0%      100.0%    0.0066      0.0080
FP8E5 x BF16              16       0.0%        0.0%      100.0%    0.0069      0.0093
FP8E5 x FP8E4             32       0.0%        0.0%      100.0%    0.0065      0.0075
FP8E5 x FP8E5             32       3.1%        3.1%      100.0%    0.0066      0.0074
------------------------------------------------------------------------------------------------

总体统计:
总测试数: 1456
C vs 参考匹配率: 735/1456 (50.48%)
C++ vs 参考匹配率: 736/1456 (50.55%)
C vs C++匹配率: 1455/1456 (99.93%)

性能对比:
平均执行时间 (参考): 0.0333ms
平均执行时间 (C):    0.0051ms
平均执行时间 (C++):  0.0046ms
C++ vs C 速度比:     1.11x

总测试时间: 0.07 秒

⚠️  发现不一致情况，需要进一步分析:
  - C方法有 721 个不匹配案例
  - C++方法有 720 个不匹配案例

测试完成!
