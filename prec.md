两种做法在「什么时候把 2-的幂次移除」与「在哪一步发生舍入」这两点上截然不同，由此带来精度与硬件一致性的差异。

────────────────────────────────────
1. 本次代码中的“整数累加-末尾统一缩放”

　前置：预对齐已经把 32 个尾数移到同一公共指数（public exp）坐标系，并把
　隐藏位左移 shift = 14(FP16/BF16) 或 6(FP8) 位:

　　raw_i = 2^shift × s_i  (s_i 在 \[-1,1) 的有符号整数)

　流程  
　a) 〈int16 / int8〉×〈int16 / int8〉 → int32  
　b) 32 次乘积累加到 int64 ——**期间完全没有舍入**  
　c) 一次性做  
　　· /2^(shiftA+shiftB)  (把左移的隐藏位还原)  
　　· ×2^(pubExpA+pubExpB) (恢复公共指数)  
　d) 再把 double → float → FP32 bits

　只有最后一步 double→float 有一次舍入；所有中间环节都是整数精确运算。

2. “标准 SEM 乘法流程”  
　—— 与 IEEE-754 单个浮点乘法完全一致，步骤为  
　　符号异或 → 指数相加 → 尾数相乘 → 结果正规化 → 舍入 → …  
　如果直接在 FP32 中实现乘-加，则  
　• 每个 \(1.M_a × 1.M_b\) 乘法结果要先舍入到 24 位有效位  
　• 乘法得到的 FP32 和累加器 (FP32) 相加时再舍入一次  
　• 32 对乘-加完成后已经发生 ≈64 次舍入

3. 精度比较  

| 项目 | 代码中新方案 | 传统 SEM 乘法后累加 (FP32) |
|------|--------------|---------------------------|
| 乘法/加法精度 | 完全整数，不舍入 | 每乘、每加都舍入 |
| 舍入次数 | 1 (最终 cast 到 float) | ≈64 (32 乘 + 32 加) |
| 理论 ulp 误差 | ≤ 0.5 ulp | 最坏可累积到几个 ulp |
| 与硬件 AI-MAC 一致性 | 高 — AI 核通常先定点化再一次性恢复指数 | 低 — 仅 CPU/GPU FPU 这样做 |

 硬件里的「定点 MAC + 统一指数」  
 • DSP / MAC 阵列只有定点乘法器，能一次跑出 *s_i × s_j*  
 • 公共指数只需在最后一次性补回来，省去动态规格化电路  
 因此我们的实现和硬件路径几乎 1:1 对应。

4. 误差示例（FP16×FP16，shift=14）  
　现实中 16-bit 尾数最大 0x7FFF，乘积 max≈2³⁰，  
　用 int64 累加 32 次上限≈2³⁶，仍远低于 64-bit 溢出。  
　经整数累加后只有一次 double→float 舍入，绝大多数
　case 可与硬件 bit-exact，且比“先除 2¹⁴ 再 FP32 乘加”多保留 ~10-11 位有效精度。

5. 规范化差异  
　• 我们在预对齐阶段已经把 32 个数“统一规格化”（右移补码），  
　　后续无需再次做 SEM 里的「结果 >2? 左移、指数+1」那步。  
　• 标准 SEM 则在每一次乘法后都要检查并调整。

────────────────────────────────────
结论  
• **更精确**：整数累加-末尾一次缩放的方案只产生一处舍入点，精度优于
　逐次 FP32 乘-加。  
• **更符合硬件**：AI/ML 加速器（包括 GPU Tensor Core、ASIC MAC 阵列）普遍
　采用「公共指数 + 定点 MAC」路线，本实现与之对齐，可做到
　bit-exact 或至少 ulp-级一致。  
如果目标是与硬件 RTL/FPGA 结果严格一致或在训练推理链路中
减少数值漂移，就应优先使用当前的整数方案；  
若只是演示算法，传统 SEM 流程也可，但其多重舍入带来的
累积误差会更大。