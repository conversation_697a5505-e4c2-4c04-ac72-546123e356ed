/**
 * @file vector_dot_product.cpp
 * @brief High-Performance Vector Dot Product Implementation
 *
 * This implementation provides optimized vector dot product computation for
 * mixed-precision data types, integrating seamlessly with the existing
 * floating-point pre-alignment system.
 *
 * Key Features:
 * - Support for all integer types (INT4, INT8, INT16) and floating-point types
 * - Robust FP32 accumulation with overflow protection
 * - Template-based optimization for compile-time specialization
 * - Integration with fp_prealign system for floating-point data
 * - C++11 compatibility with comprehensive error handling
 *
 */

#include "../inc/vector_dot_product.hpp"
#include <algorithm>
#include <cmath>
#include <cstring>

using namespace fp_prealign;
using namespace vector_dot_product;

namespace vector_dot_product {

// Forward declaration so it can be used before its definition
static uint32_t double_to_fp32_trunc(double value);

// IEEE 754 FP32 bit manipulation utilities
union FP32Union {
    float f;
    uint32_t u;
    struct {
        uint32_t mantissa : 23;
        uint32_t exponent : 8;
        uint32_t sign : 1;
    } bits;
};

/**
 * @brief Convert 64-bit integer accumulator to IEEE 754 FP32
 */
uint32_t IntegerAccumulationStrategy::convert_to_fp32(int64_t accumulator)
{
    if (accumulator == 0) {
        return 0; // Positive zero
    }

    return double_to_fp32_trunc(static_cast<double>(accumulator));
}

/**
 * @brief Accumulate floating-point products with proper scaling
 */
void FloatingPointAccumulationStrategy::accumulate_product(uint16_t a_mantissa, uint16_t b_mantissa, float& accumulator)
{
    float a_float = mantissa_to_float(a_mantissa);
    float b_float = mantissa_to_float(b_mantissa);
    accumulator += a_float * b_float;
}

/**
 * @brief Calculate scaling factor from public exponents
 */
float FloatingPointAccumulationStrategy::calculate_scaling_factor(uint8_t a_public_exp, uint8_t b_public_exp)
{
    // Combined exponent for the dot product result
    int32_t combined_exp = static_cast<int32_t>(a_public_exp) + static_cast<int32_t>(b_public_exp);

    // Convert to FP32 scaling factor
    // Note: This follows the same logic as dcim_macro.c for exponent handling
    return std::pow(2.0f, static_cast<float>(combined_exp));
}

/**
 * @brief Generate final FP32 result with proper exponent scaling
 */
uint32_t FloatingPointAccumulationStrategy::generate_fp32_result(float accumulator, uint8_t a_public_exp, uint8_t b_public_exp)
{
    if (accumulator == 0.0f) {
        return 0; // Positive zero
    }

    float scaling_factor = calculate_scaling_factor(a_public_exp, b_public_exp);
    float scaled_result = accumulator * scaling_factor;

    return double_to_fp32_trunc(scaled_result);
}

/**
 * @brief Template specialization for integer-integer dot product
 */
template <uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_integer_integer(const uint16_t* a_data, const uint16_t* b_data)
{
    // Note: Type checking is done at runtime in compute_dot_product

    int64_t accumulator = 0;

    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        int32_t a_val = DataTypeConverter::convert_integer_data(a_data[i], DataTypeA);
        int32_t b_val = DataTypeConverter::convert_integer_data(b_data[i], DataTypeB);

        IntegerAccumulationStrategy::accumulate_product(a_val, b_val, accumulator);
    }

    return IntegerAccumulationStrategy::convert_to_fp32(accumulator);
}

/**
 * @brief Template specialization for floating-floating dot product
 */
template <uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_floating_floating(const uint16_t* a_data, const uint16_t* b_data)
{
    int64_t accumulator = 0;

    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        int32_t a_val = DataTypeConverter::template convert_mantissa_to_int32<DataTypeA>(a_data[i]);
        int32_t b_val = DataTypeConverter::template convert_mantissa_to_int32<DataTypeB>(b_data[i]);
        accumulator += static_cast<int64_t>(a_val) * static_cast<int64_t>(b_val);
    }

    // Combined mantissa shift introduced during pre-alignment
    constexpr int total_shift = DataTypeTraits<DataTypeA>::mantissa_shift + DataTypeTraits<DataTypeB>::mantissa_shift;

    // Exponent contribution from public exponents stored at index 32
    uint8_t a_public_exp = static_cast<uint8_t>(a_data[VECTOR_SIZE]);
    uint8_t b_public_exp = static_cast<uint8_t>(b_data[VECTOR_SIZE]);
    int exp_sum = static_cast<int>(a_public_exp) + static_cast<int>(b_public_exp);

    // Convert to double for final scaling, then to float
    double scaled = static_cast<double>(accumulator);
    scaled = std::ldexp(scaled, -total_shift); // divide by 2^total_shift
    scaled = std::ldexp(scaled, exp_sum); // multiply by 2^(exp_sum)

    return double_to_fp32_trunc(scaled);
}

/**
 * @brief Template specialization for mixed-precision dot product
 */
template <uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_mixed_precision(const uint16_t* a_data, const uint16_t* b_data)
{
    // One operand is integer, the other floating point
    double accumulator = 0.0;

    const bool a_is_int = (DataTypeTraits<DataTypeA>::type_class == DataTypeClass::INTEGER);

    if (a_is_int) {
        // A integer, B floating
        uint8_t b_public_exp = static_cast<uint8_t>(b_data[VECTOR_SIZE]);
        constexpr int shift_b = DataTypeTraits<DataTypeB>::mantissa_shift;

        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            int32_t a_val = DataTypeConverter::convert_integer_data(a_data[i], DataTypeA);
            int32_t b_val = DataTypeConverter::template convert_mantissa_to_int32<DataTypeB>(b_data[i]);
            accumulator += static_cast<double>(a_val) * static_cast<double>(b_val);
        }

        // Apply scaling: divide by 2^shift_b then multiply by 2^public_exp
        accumulator = std::ldexp(accumulator, -shift_b);
        accumulator = std::ldexp(accumulator, b_public_exp);
    } else {
        // A floating, B integer
        uint8_t a_public_exp = static_cast<uint8_t>(a_data[VECTOR_SIZE]);
        constexpr int shift_a = DataTypeTraits<DataTypeA>::mantissa_shift;

        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            int32_t a_val = DataTypeConverter::template convert_mantissa_to_int32<DataTypeA>(a_data[i]);
            int32_t b_val = DataTypeConverter::convert_integer_data(b_data[i], DataTypeB);
            accumulator += static_cast<double>(a_val) * static_cast<double>(b_val);
        }

        accumulator = std::ldexp(accumulator, -shift_a);
        accumulator = std::ldexp(accumulator, a_public_exp);
    }

    return double_to_fp32_trunc(accumulator);
}

/**
 * @brief Main template function implementation
 */
template <uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t VectorDotProductEngine<DataTypeA, DataTypeB>::compute_dot_product(const uint16_t* a_data, const uint16_t* b_data)
{
    if (both_integer) {
        return compute_integer_integer(a_data, b_data);
    } else if (both_floating) {
        return compute_floating_floating(a_data, b_data);
    } else {
        return compute_mixed_precision(a_data, b_data);
    }
}

/**
 * @brief Main template function wrapper
 */
template <uint8_t DataTypeA, uint8_t DataTypeB>
uint32_t compute_vector_dot_product(const uint16_t* a_data, const uint16_t* b_data)
{
    return VectorDotProductEngine<DataTypeA, DataTypeB>::compute_dot_product(a_data, b_data);
}

// Explicit template instantiations for all supported combinations
// Note: Only instantiate the main template function, not the specialized internal functions
// The internal functions will be called based on runtime logic in compute_dot_product

// Integer-Integer combinations
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_INT16>(const uint16_t*, const uint16_t*);

// Floating-Floating combinations
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_FP8E5>(const uint16_t*, const uint16_t*);

// Mixed-precision combinations (Integer-Floating)
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT4, VDP_FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT8, VDP_FP8E5>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_FP16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_BF16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_FP8E4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_INT16, VDP_FP8E5>(const uint16_t*, const uint16_t*);

// Mixed-precision combinations (Floating-Integer)
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP16, VDP_INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_BF16, VDP_INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E4, VDP_INT16>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_INT4>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_INT8>(const uint16_t*, const uint16_t*);
template uint32_t compute_vector_dot_product<VDP_FP8E5, VDP_INT16>(const uint16_t*, const uint16_t*);

// Helper: convert double to FP32 bit-pattern **by truncation**, mimicking the bit extraction
// logic used in dcim_macro_com(). This avoids the default round-to-nearest behaviour of
// static_cast<float>() so results stay bit-exact with the C reference implementation.
static uint32_t double_to_fp32_trunc(double value)
{
    if (value == 0.0) {
        return 0u; // +0.0f
    }

    // Handle sign
    uint32_t sign_bit = 0u;
    if (value < 0.0) {
        sign_bit = 0x80000000u;
        value = -value;
    }

    // Decompose value = frac * 2^exp with 0.5 <= frac < 1.0
    int exp;
    double frac = std::frexp(value, &exp); // value = frac * 2^exp

    // Bring frac into [1,2) so that the implicit leading 1 is explicit.
    frac *= 2.0;
    exp  -= 1; // because we multiplied frac by 2

    // Bias exponent for IEEE-754 single precision (127 bias).
    int32_t exp_bits = exp + 127;
    if (exp_bits <= 0) {
        // Underflow to sub-normals – for our use-cases (post-scaled MAC) this should
        // be extremely rare. We simply return zero to stay consistent with the C path
        // which also flushes very small magnitudes.
        return 0u | sign_bit;
    }
    if (exp_bits >= 255) {
        // Overflow – saturate to Inf with the correct sign, mirroring IEEE-754 behaviour.
        return sign_bit | 0x7F800000u;
    }

    // Extract 23 fraction bits **without rounding** (truncate).
    double mant = frac - 1.0;           // range [0,1)
    double mant_scaled = mant * static_cast<double>(1u << 23);
    uint32_t mant_bits = static_cast<uint32_t>(mant_scaled); // truncation here

    return sign_bit | (static_cast<uint32_t>(exp_bits) << 23) | (mant_bits & 0x7FFFFFu);
}

} // namespace vector_dot_product

/**
 * @brief Implementation of pre-alignment integration utilities
 */
bool PreAlignmentIntegration::prealign_if_needed(const uint16_t* input_data, uint8_t data_type, uint16_t* output_data)
{
    if (!input_data || !output_data) {
        return false;
    }

    if (!requires_prealignment(data_type)) {
        // For integer types, just copy the data
        std::memcpy(output_data, input_data, vector_dot_product::VECTOR_SIZE * sizeof(uint16_t));
        return true;
    }

    // Use the existing fp_prealign system for floating-point types
    switch (data_type) {
    case vector_dot_product::VDP_FP16:
        fp_prealign::float_prealign<fp_prealign::FP16>(input_data, output_data);
        return true;
    case vector_dot_product::VDP_BF16:

        fp_prealign::float_prealign<fp_prealign::BF16>(input_data, output_data);
        return true;
    case vector_dot_product::VDP_FP8E4:
        fp_prealign::float_prealign<fp_prealign::FP8E4>(input_data, output_data);
        return true;
    case vector_dot_product::VDP_FP8E5:
        fp_prealign::float_prealign<fp_prealign::FP8E5>(input_data, output_data);
        return true;
    default:
        return false;
    }
}

bool PreAlignmentIntegration::validate_inputs(const uint16_t* a_data, uint8_t a_data_type,
    const uint16_t* b_data, uint8_t b_data_type)
{
    // Check for null pointers
    if (!a_data || !b_data) {
        return false;
    }

    // Check for valid data types
    bool a_valid = (a_data_type >= vector_dot_product::VDP_INT4 && a_data_type <= vector_dot_product::VDP_FP8E5);
    bool b_valid = (b_data_type >= vector_dot_product::VDP_INT4 && b_data_type <= vector_dot_product::VDP_FP8E5);

    return a_valid && b_valid;
}

/**
 * @brief Implementation of high-level vector dot product interface
 */
uint32_t VectorDotProductInterface::compute_with_auto_prealign(const uint16_t* a_raw, uint8_t a_data_type,
    const uint16_t* b_raw, uint8_t b_data_type)
{
    if (!PreAlignmentIntegration::validate_inputs(a_raw, a_data_type, b_raw, b_data_type)) {
        return 0; // Invalid inputs
    }

    // Prepare data buffers
    uint16_t a_processed[vector_dot_product::PREALIGNED_SIZE];
    uint16_t b_processed[vector_dot_product::PREALIGNED_SIZE];

    // Pre-align data if needed
    if (!PreAlignmentIntegration::prealign_if_needed(a_raw, a_data_type, a_processed) || !PreAlignmentIntegration::prealign_if_needed(b_raw, b_data_type, b_processed)) {
        return 0; // Pre-alignment failed
    }

    // Compute dot product using pre-aligned data
    return compute_with_prealigned(a_processed, a_data_type, b_processed, b_data_type);
}

uint32_t VectorDotProductInterface::compute_with_prealigned(const uint16_t* a_prealigned, uint8_t a_data_type,
    const uint16_t* b_prealigned, uint8_t b_data_type)
{
    if (!PreAlignmentIntegration::validate_inputs(a_prealigned, a_data_type, b_prealigned, b_data_type)) {
        return 0; // Invalid inputs
    }

    // Delegate to the C wrapper function which handles all type combinations
    return ::vector_dot_product_c(a_prealigned, a_data_type, b_prealigned, b_data_type);
}

// C-compatible wrapper implementation
extern "C" {

/**
 * @brief C wrapper for vector dot product computation with comprehensive error handling
 */
uint32_t vector_dot_product_c(const uint16_t* a_data, uint8_t a_data_type,
    const uint16_t* b_data, uint8_t b_data_type)
{
    // Input validation
    if (!a_data || !b_data) {
        return 0; // Return zero for null pointers
    }

    // Dispatch to appropriate template instantiation based on data types
    // This follows the same pattern as the fp_prealign system
    switch (a_data_type) {
    case vector_dot_product::VDP_INT4:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT4, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_INT8:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT8, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_INT16:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_INT16, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_FP16:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP16, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_BF16: 
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_BF16, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_FP8E4:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E4, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    case vector_dot_product::VDP_FP8E5:
        switch (b_data_type) {
        case vector_dot_product::VDP_INT4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_INT4>(a_data, b_data);
        case vector_dot_product::VDP_INT8:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_INT8>(a_data, b_data);
        case vector_dot_product::VDP_INT16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_INT16>(a_data, b_data);
        case vector_dot_product::VDP_FP16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_FP16>(a_data, b_data);
        case vector_dot_product::VDP_BF16:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_BF16>(a_data, b_data);
        case vector_dot_product::VDP_FP8E4:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_FP8E4>(a_data, b_data);
        case vector_dot_product::VDP_FP8E5:
            return vector_dot_product::compute_vector_dot_product<vector_dot_product::VDP_FP8E5, vector_dot_product::VDP_FP8E5>(a_data, b_data);
        default:
            return 0;
        }
    default:
        return 0; // Unsupported data type
    }
}

/**
 * @brief C wrapper with automatic pre-alignment
 */
uint32_t vector_dot_product_auto_c(const uint16_t* a_raw, uint8_t a_data_type,
    const uint16_t* b_raw, uint8_t b_data_type)
{
    return VectorDotProductInterface::compute_with_auto_prealign(
        a_raw, a_data_type, b_raw, b_data_type);
}

} // extern "C"
