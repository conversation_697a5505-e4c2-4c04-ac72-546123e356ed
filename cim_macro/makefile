CC = gcc
CXX = g++

SRCDIR = ./src
INCDIR = ./inc
TESTDIR = ./test
BUILDDIR = ./
TARGET = dcim
TEST_TARGET = test_vector_dot_product
COMPARISON_TEST_TARGET = test_comparison_three_methods

CFLAGS = -Wall -I$(INCDIR) -std=c99 -g
CXXFLAGS = -Wall -I$(INCDIR) -std=c++11 -g
LDFLAGS = -lm

# Source files
C_SOURCES = $(wildcard $(SRCDIR)/*.c)
CPP_SOURCES = $(wildcard $(SRCDIR)/*.cpp)
C_OBJECTS = $(patsubst $(SRCDIR)/%.c, $(BUILDDIR)/%.o, $(C_SOURCES))
CPP_OBJECTS = $(patsubst $(SRCDIR)/%.cpp, $(BUILDDIR)/%.o, $(CPP_SOURCES))
ALL_OBJECTS = $(C_OBJECTS) $(CPP_OBJECTS)

# Test files
TEST_SOURCES = $(TESTDIR)/test_vector_dot_product.cpp
TEST_OBJECTS = $(patsubst $(TESTDIR)/%.cpp, $(BUILDDIR)/test_%.o, $(TEST_SOURCES))

COMPARISON_TEST_SOURCES = $(TESTDIR)/test_comparison_three_methods.cpp
COMPARISON_TEST_OBJECTS = $(patsubst $(TESTDIR)/%.cpp, $(BUILDDIR)/test_%.o, $(COMPARISON_TEST_SOURCES))

# Default target
all: $(BUILDDIR)/$(TARGET)

# Main executable
$(BUILDDIR)/$(TARGET): $(ALL_OBJECTS)
	$(CXX) $^ -o $@ $(LDFLAGS)

# Test executable
$(BUILDDIR)/$(TEST_TARGET): $(TEST_OBJECTS) $(CPP_OBJECTS) $(C_OBJECTS)
	$(CXX) $^ -o $@ $(LDFLAGS)

# Comparison test executable
$(BUILDDIR)/$(COMPARISON_TEST_TARGET): $(COMPARISON_TEST_OBJECTS) $(CPP_OBJECTS) $(C_OBJECTS)
	$(CXX) $^ -o $@ $(LDFLAGS)

# C source compilation
$(BUILDDIR)/%.o: $(SRCDIR)/%.c
	@mkdir -p $(BUILDDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# C++ source compilation
$(BUILDDIR)/%.o: $(SRCDIR)/%.cpp
	@mkdir -p $(BUILDDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Test compilation
$(BUILDDIR)/test_%.o: $(TESTDIR)/%.cpp
	@mkdir -p $(BUILDDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Test targets
test: $(BUILDDIR)/$(TEST_TARGET)
	./$(BUILDDIR)/$(TEST_TARGET)

test_comparison: $(BUILDDIR)/$(COMPARISON_TEST_TARGET)
	./$(BUILDDIR)/$(COMPARISON_TEST_TARGET)

# Build all tests
all_tests: $(BUILDDIR)/$(TEST_TARGET) $(BUILDDIR)/$(COMPARISON_TEST_TARGET)

# Clean target
clean:
	rm -f $(BUILDDIR)/*.o $(BUILDDIR)/$(TARGET) $(BUILDDIR)/$(TEST_TARGET) $(BUILDDIR)/$(COMPARISON_TEST_TARGET)

.PHONY: all test test_comparison all_tests clean
