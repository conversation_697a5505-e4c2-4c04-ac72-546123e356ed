/**
 * @file test_vector_dot_product.cpp
 * @brief Comprehensive Test Suite for Vector Dot Product System
 *
 * This test suite validates the vector dot product implementation for all
 * supported data type combinations using real test data from weight_table and
 * input_table, with reference calculations using ac_datatypes.
 *
 * Test Coverage:
 * - Integer types: INT4, INT8, INT16 (no pre-alignment required)
 * - Floating-point types: FP16, BF16, FP8E4, FP8E5 (pre-alignment required)
 * - All combinations: same precision and mixed precision
 * - Real weight vectors and input data from tables
 * - Reference calculation using ac_datatypes for validation
 */

#include <iostream>
#include <iomanip>
#include <cmath>
#include <cstring>
#include <vector>
#include <string>
#include <sstream>
#include <chrono>

#include "../inc/vector_dot_product.hpp"
#include "../inc/weight_table.h"
#include "../inc/input_table.h"
#include "ac_datatypes.h"

// Test configuration constants
static constexpr float TOLERANCE_INTEGER = 1e-6f;
static constexpr float TOLERANCE_FLOATING = 1e-3f;  // More relaxed tolerance for floating-point

// Convenient type aliases using the new enum values
using namespace vector_dot_product;

/**
 * @brief Test result structure for comprehensive validation
 */
struct TestResult {
    bool passed;
    std::string test_name;
    std::string error_message;
    double execution_time_ms;
    uint32_t computed_result;
    float computed_float;
    float reference_float;
    float absolute_error;
    
    TestResult(const std::string& name) 
        : passed(false), test_name(name), execution_time_ms(0.0), 
          computed_result(0), computed_float(0.0f), reference_float(0.0f), absolute_error(0.0f) {}
};

/**
 * @brief Test utilities for data type handling and validation
 */
class TestUtilities {
public:
    /**
     * @brief Convert data to float representation for display (supports all types)
     */
    static std::vector<float> convert_to_float_display(const uint16_t* data, uint8_t data_type) {
        std::vector<float> result(32);
        
        for (size_t i = 0; i < 32; ++i) {
            switch (data_type) {
                case VDP_INT4: {
                    ac_int4_t val = data[i];
                    result[i] = static_cast<float>(val.to_int());
                    break;
                }
                case VDP_INT8: {
                    ac_int8_t val = data[i];
                    result[i] = static_cast<float>(val.to_int());
                    break;
                }
                case VDP_INT16: {
                    ac_int16_t val = data[i];
                    result[i] = static_cast<float>(val.to_int());
                    break;
                }
                case VDP_FP16: {
                    ac_float16_t val ;
                    val.set_data(data[i]);
                    result[i] = val.to_float();
                    break;
                }
                case VDP_BF16: {
                    ac_bfloat16_t val;
                    val.set_data(data[i]);
                    result[i] = val.to_float();
                    break;
                }
                case VDP_FP8E4: {
                    ac_fp8e4_t val;
                    val.set_data(data[i]);
                    result[i] = val.to_float();
                    break;
                }
                case VDP_FP8E5: {
                    ac_fp8e5_t val;
                    val.set_data(data[i]);
                    result[i] = val.to_float();
                    break;
                }
                default:
                    result[i] = 0.0f;
                    break;
            }
        }
        return result;
    }
    
    /**
     * @brief Calculate reference dot product using ac_datatypes
     */
    static float calculate_reference_dot_product(const uint16_t* a_data, uint8_t a_type,
                                                const uint16_t* b_data, uint8_t b_type) {
        double accumulator = 0.0;
        
        for (size_t i = 0; i < 32; ++i) {
            double a_val = 0.0, b_val = 0.0;
            
            // Convert A data
            switch (a_type) {
                case VDP_INT4: {
                    ac_int4_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT8: {
                    ac_int8_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT16: {
                    ac_int16_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_FP16: {
                    ac_float16_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_BF16: {
                    ac_bfloat16_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_FP8E4: {
                    ac_fp8e4_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_FP8E5: {
                    ac_fp8e5_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
            }
            
            // Convert B data
            switch (b_type) {
                case VDP_INT4: {
                    ac_int4_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT8: {
                    ac_int8_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT16: {
                    ac_int16_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_FP16: {
                    ac_float16_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_BF16: {
                    ac_bfloat16_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_FP8E4: {
                    ac_fp8e4_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_FP8E5: {
                    ac_fp8e5_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
            }
            
            accumulator += a_val * b_val;
        }
        
        return static_cast<float>(accumulator);
    }
    
    /**
     * @brief Compare results with appropriate tolerance
     */
    static bool compare_results(float computed, float reference, float tolerance) {
        // Handle special cases
        if (std::isnan(computed) && std::isnan(reference)) return true;
        if (std::isinf(computed) && std::isinf(reference)) return true;
        if (computed == 0.0f && reference == 0.0f) return true;
        
        // Use relative tolerance for larger values, absolute for smaller ones
        float abs_diff = std::abs(computed - reference);
        float rel_tolerance = tolerance * std::max(std::abs(computed), std::abs(reference));
        
        return abs_diff <= std::max(tolerance, rel_tolerance);
    }
    
    /**
     * @brief Get data type name for reporting
     */
    static std::string get_data_type_name(uint8_t data_type) {
        switch (data_type) {
            case VDP_INT4: return "INT4";
            case VDP_INT8: return "INT8";
            case VDP_INT16: return "INT16";
            case VDP_FP16: return "FP16";
            case VDP_BF16: return "BF16";
            case VDP_FP8E4: return "FP8E4";
            case VDP_FP8E5: return "FP8E5";
            default: return "UNKNOWN";
        }
    }
    
    /**
     * @brief Check if data type is floating-point
     */
    static bool is_floating_point_type(uint8_t data_type) {
        return (data_type == VDP_FP16 || data_type == VDP_BF16 || 
                data_type == VDP_FP8E4 || data_type == VDP_FP8E5);
    }
    
    /**
     * @brief Get appropriate tolerance for data type combination
     */
    static float get_tolerance_for_types(uint8_t a_type, uint8_t b_type) {
        if (is_floating_point_type(a_type) || is_floating_point_type(b_type)) {
            return TOLERANCE_FLOATING;
        }
        return TOLERANCE_INTEGER;
    }
    
    /**
     * @brief Get number of weight vectors for a data type
     */
    static int get_weight_vector_count(uint8_t data_type) {
        switch (data_type) {
            case VDP_INT4: return 64;
            case VDP_INT8: return 32;
            case VDP_INT16: return 16;
            case VDP_FP16: return 16;
            case VDP_BF16: return 16;
            case VDP_FP8E4: return 32;
            case VDP_FP8E5: return 32;
            default: return 0;
        }
    }
    
    /**
     * @brief Print vector as float values (first 8 and last 8 elements)
     */
    static void print_vector_summary(const std::vector<float>& vec, const std::string& label) {
        std::cout << label << ": [";
        // Print first 8 elements
        for (size_t i = 0; i < std::min(size_t(8), vec.size()); ++i) {
            if (i > 0) std::cout << ", ";
            std::cout << std::fixed << std::setprecision(3) << vec[i];
        }
        if (vec.size() > 16) {
            std::cout << " ... ";
            // Print last 8 elements
            for (size_t i = vec.size() - 8; i < vec.size(); ++i) {
                std::cout << std::fixed << std::setprecision(3) << vec[i];
                if (i < vec.size() - 1) std::cout << ", ";
            }
        } else if (vec.size() > 8) {
            std::cout << ", ";
            for (size_t i = 8; i < vec.size(); ++i) {
                std::cout << std::fixed << std::setprecision(3) << vec[i];
                if (i < vec.size() - 1) std::cout << ", ";
            }
        }
        std::cout << "]" << std::endl;
    }
};

/**
 * @brief Weight data accessor for all supported types
 */
class WeightDataAccessor {
public:
    /**
     * @brief Get weight data for specified type and index (always returns 32-element unaligned data)
     */
    static bool get_weight_data(uint8_t weight_type, int weight_index, uint16_t* output_data) {
        switch (weight_type) {
            case VDP_INT4:
                if (weight_index >= 64) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int4[weight_index][i];
                }
                break;
            case VDP_INT8:
                if (weight_index >= 32) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int8[weight_index][i];
                }
                break;
            case VDP_INT16:
                if (weight_index >= 16) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int16[weight_index][i];
                }
                break;
            case VDP_FP16:
                if (weight_index >= 16) return false;
                // Use unaligned data - let calculate_reference_dot_product handle alignment
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp16[weight_index][i];
                }
                break;
            case VDP_BF16:
                if (weight_index >= 16) return false;
                // Use unaligned data - let calculate_reference_dot_product handle alignment
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_bf16[weight_index][i];
                }
                break;
            case VDP_FP8E4:
                if (weight_index >= 32) return false;
                // Use unaligned data - let calculate_reference_dot_product handle alignment
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp8e4[weight_index][i];
                }
                break;
            case VDP_FP8E5:
                if (weight_index >= 32) return false;
                // Use unaligned data - let calculate_reference_dot_product handle alignment
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp8e5[weight_index][i];
                }
                break;
            default:
                return false;
        }
        return true;
    }
};

/**
 * @brief Input data accessor for all supported types
 */
class InputDataAccessor {
public:
    /**
     * @brief Get input data for specified type
     */
    static bool get_input_data(uint8_t input_type, uint16_t* output_data) {
        switch (input_type) {
            case VDP_INT4:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int4[VDP_INT4][i];  // Use first input vector
                }
                break;
            case VDP_INT8:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int8[VDP_INT8][i];  // Use second input vector
                }
                break;
            case VDP_INT16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int16[VDP_INT16][i];  // Use third input vector
                }
                break;
            case VDP_FP16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp16[VDP_FP16-1][i];  // Use first FP16 input vector
                }
                break;
            case VDP_BF16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_bf16[VDP_BF16-1][i];  // Use first BF16 input vector
                }
                break;
            case VDP_FP8E4:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp8e4[VDP_FP8E4-1][i];  // Use first FP8E4 input vector
                }
                break;
            case VDP_FP8E5:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp8e5[VDP_FP8E5-1][i];  // Use first FP8E5 input vector
                }
                break;
            default:
                return false;
        }
        return true;
    }
};

/**
 * @brief Universal test runner for all data type combinations
 */
class UniversalVectorDotProductTester {
private:
    std::vector<TestResult> test_results;
    
public:
    /**
     * @brief Test a specific input-weight combination
     */
    TestResult test_single_combination(uint8_t input_type, uint8_t weight_type, int weight_index) {
        std::string test_name = TestUtilities::get_data_type_name(input_type) + " x " + 
                               TestUtilities::get_data_type_name(weight_type) + 
                               " (weight #" + std::to_string(weight_index) + ")";
        TestResult result(test_name);
        
        try {
            // Initialize data buffers to zero (important for element 33 which represents exponent for integer types)
            uint16_t input_data[64] = {0};  // Initialize all elements to 0
            uint16_t weight_data[64] = {0}; // Initialize all elements to 0
            
            if (!InputDataAccessor::get_input_data(input_type, input_data)) {
                result.error_message = "Failed to get input data";
                return result;
            }
            
            if (!WeightDataAccessor::get_weight_data(weight_type, weight_index, weight_data)) {
                result.error_message = "Failed to get weight data";
                return result;
            }
            
            // Convert to float for display (only for first test of each type combination)
            if (weight_index == 0) {
                auto input_floats = TestUtilities::convert_to_float_display(input_data, input_type);
                auto weight_floats = TestUtilities::convert_to_float_display(weight_data, weight_type);
                
                std::cout << "\n=== " << TestUtilities::get_data_type_name(input_type) << " x " 
                         << TestUtilities::get_data_type_name(weight_type) << " ===\n";
                TestUtilities::print_vector_summary(input_floats, "Input A (" + TestUtilities::get_data_type_name(input_type) + ")");
                TestUtilities::print_vector_summary(weight_floats, "Weight B (" + TestUtilities::get_data_type_name(weight_type) + ") #0");
            }
            
            // Calculate reference result
            result.reference_float = TestUtilities::calculate_reference_dot_product(
                input_data, input_type, weight_data, weight_type);
            
            // Compute dot product using our implementation
            auto start_time = std::chrono::high_resolution_clock::now();
            
            result.computed_result = VectorDotProductInterface::compute_with_auto_prealign(
                input_data, input_type, weight_data, weight_type);
            
            auto end_time = std::chrono::high_resolution_clock::now();
            result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
            
            // Convert computed result to float
            memcpy(&result.computed_float, &result.computed_result, sizeof(float));
            
            // Calculate error
            result.absolute_error = std::abs(result.computed_float - result.reference_float);
            
            // Check if result is acceptable
            float tolerance = TestUtilities::get_tolerance_for_types(input_type, weight_type);
            result.passed = TestUtilities::compare_results(
                result.computed_float, result.reference_float, tolerance);
            
            // Print detailed results for first weight of each combination or failed tests
            if (weight_index == 0 || !result.passed) {
                std::cout << std::fixed << std::setprecision(6);
                std::cout << "Weight #" << weight_index << " - Reference: " << result.reference_float 
                         << ", Computed: " << result.computed_float 
                         << ", Error: " << result.absolute_error;
                if (result.passed) {
                    std::cout << " ✓ PASSED";
                } else {
                    std::cout << " ✗ FAILED (tolerance: " << tolerance << ")";
                }
                std::cout << std::endl;
            }
            
            if (!result.passed) {
                // Provide detailed error message with numerical values
                std::ostringstream error_stream;
                error_stream << std::fixed << std::setprecision(6);
                error_stream << "Computed result differs from reference beyond tolerance. ";
                error_stream << "Reference: " << result.reference_float;
                error_stream << ", Computed: " << result.computed_float;
                error_stream << ", Error: " << result.absolute_error;
                error_stream << ", Tolerance: " << tolerance;
                result.error_message = error_stream.str();
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.error_message = std::string("Exception: ") + e.what();
            std::cout << "Error in " << test_name << ": " << result.error_message << std::endl;
        } catch (...) {
            result.passed = false;
            result.error_message = "Unknown exception occurred";
            std::cout << "Error in " << test_name << ": " << result.error_message << std::endl;
        }
        
        return result;
    }
    
    /**
     * @brief Test all combinations for a specific input-weight type pair
     */
    void test_type_combination(uint8_t input_type, uint8_t weight_type) {
        int weight_count = TestUtilities::get_weight_vector_count(weight_type);
        int passed_count = 0;
        int failed_count = 0;
        
        std::cout << "\n--- Testing " << TestUtilities::get_data_type_name(input_type) 
                  << " x " << TestUtilities::get_data_type_name(weight_type) 
                  << " (" << weight_count << " weight vectors) ---" << std::endl;
        
        for (int weight_idx = 0; weight_idx < weight_count; ++weight_idx) {
            TestResult result = test_single_combination(input_type, weight_type, weight_idx);
            test_results.push_back(result);
            
            if (result.passed) {
                passed_count++;
            } else {
                failed_count++;
            }
        }
        
        std::cout << "Summary for " << TestUtilities::get_data_type_name(input_type) 
                  << " x " << TestUtilities::get_data_type_name(weight_type) 
                  << ": " << passed_count << "/" << weight_count << " passed";
        if (failed_count > 0) {
            std::cout << " (" << failed_count << " failed)";
        }
        std::cout << std::endl;
    }
    
    /**
     * @brief Run comprehensive test suite for all supported combinations
     */
    void run_comprehensive_tests() {
        std::cout << "=== Comprehensive Vector Dot Product Test Suite ===" << std::endl;
        std::cout << "Testing all supported data type combinations" << std::endl;
        std::cout << "Integer types: INT4, INT8, INT16" << std::endl;
        std::cout << "Floating-point types: FP16, BF16, FP8E4, FP8E5" << std::endl;
        std::cout << "Weight vector counts: INT4=64, INT8=32, INT16=16, FP16=16, BF16=16, FP8E4=32, FP8E5=32" << std::endl;
        
        std::vector<uint8_t> all_types = {VDP_INT4, VDP_INT8, VDP_INT16, VDP_FP16, VDP_BF16, VDP_FP8E4, VDP_FP8E5};
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // Test all combinations
        for (uint8_t input_type : all_types) {
            for (uint8_t weight_type : all_types) {
                test_type_combination(input_type, weight_type);
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        double total_time = std::chrono::duration<double, std::milli>(end_time - start_time).count();
        
        // Print final summary
        int total_tests = test_results.size();
        int total_passed = 0;
        int total_failed = 0;
        
        for (const auto& result : test_results) {
            if (result.passed) {
                total_passed++;
            } else {
                total_failed++;
            }
        }
        
        std::cout << "\n=== FINAL TEST SUMMARY ===" << std::endl;
        std::cout << "Total tests: " << total_tests << std::endl;
        std::cout << "Passed: " << total_passed << std::endl;
        std::cout << "Failed: " << total_failed << std::endl;
        std::cout << "Success rate: " << std::fixed << std::setprecision(1) 
                  << (100.0 * total_passed / total_tests) << "%" << std::endl;
        std::cout << "Total execution time: " << std::fixed << std::setprecision(2) 
                  << total_time << " ms" << std::endl;
        
        if (total_failed > 0) {
            std::cout << "\nFailed tests:" << std::endl;
            for (const auto& result : test_results) {
                if (!result.passed) {
                    std::cout << "  - " << result.test_name << ": " << result.error_message << std::endl;
                }
            }
        }
    }
};

int main() {
    std::cout << "Vector Dot Product Comprehensive Test Suite" << std::endl;
    std::cout << "==========================================" << std::endl;
    
    try {
        UniversalVectorDotProductTester tester;
        tester.run_comprehensive_tests();
        
        std::cout << "\nTest suite completed successfully!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Test suite failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test suite failed with unknown exception" << std::endl;
        return 1;
    }
}
