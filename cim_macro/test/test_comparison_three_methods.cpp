/**
 * @file test_comparison_three_methods.cpp
 * @brief 三种计算方法对比测试系统
 *
 * 该测试系统对比三种向量点积计算方法：
 * 1. reference: ac_datatypes（黄金标准参考实现）
 * 2. c_cal: dcim_macro_com（C语言实现）
 * 3. cpp_cal: VectorDotProductInterface（C++实现）
 *
 * 测试覆盖：
 * - 所有支持的数据类型组合（INT4, INT8, INT16, FP16, BF16, FP8E4, FP8E5）
 * - 详细的误差分析和统计
 * - 性能对比
 * - 一致性验证
 */

#include <iostream>
#include <iomanip>
#include <cmath>
#include <cstring>
#include <vector>
#include <string>
#include <sstream>
#include <chrono>
#include <unordered_map>

#include "../inc/vector_dot_product.hpp"
#include "../inc/weight_table.h"
#include "../inc/input_table.h"
#include "../inc/dcim_com.h"
#include "../inc/fp_prealign.hpp"
#include "ac_datatypes.h"

// 测试配置常量
static constexpr float TOLERANCE_INTEGER = 1e-6f;
static constexpr float TOLERANCE_FLOATING = 1e-9f;

// 使用新的枚举值
using namespace vector_dot_product;

/**
 * @brief 三种方法的计算结果结构
 */
struct TripleResult {
    float reference_result;     // ac_datatypes参考结果
    float c_result;            // C语言计算结果
    float cpp_result;          // C++计算结果
    
    double reference_time_ms;   // 参考方法执行时间
    double c_time_ms;          // C方法执行时间
    double cpp_time_ms;        // C++方法执行时间
    
    uint32_t reference_raw;     // 参考结果原始位表示
    uint32_t c_raw;            // C结果原始位表示
    uint32_t cpp_raw;          // C++结果原始位表示
    
    float c_vs_ref_error;      // C vs 参考的误差
    float cpp_vs_ref_error;    // C++ vs 参考的误差
    float c_vs_cpp_error;      // C vs C++的误差
    
    bool c_matches_ref;        // C是否与参考匹配
    bool cpp_matches_ref;      // C++是否与参考匹配
    bool c_matches_cpp;        // C是否与C++匹配
};

/**
 * @brief 测试结果汇总结构
 */
struct TestSummary {
    std::string test_name;
    int total_tests;
    int c_ref_matches;
    int cpp_ref_matches;
    int c_cpp_matches;
    double avg_c_ref_error;
    double avg_cpp_ref_error;
    double avg_c_cpp_error;
    double max_c_ref_error;
    double max_cpp_ref_error;
    double max_c_cpp_error;
    double avg_c_time_ms;
    double avg_cpp_time_ms;
    double avg_ref_time_ms;
};

/**
 * @brief 测试工具类 - 扩展版本
 */
class ComparisonTestUtilities {
public:
    /**
     * @brief 使用ac_datatypes计算参考点积结果
     */
    static float calculate_reference_dot_product(const uint16_t* a_data, uint8_t a_type,
                                                const uint16_t* b_data, uint8_t b_type) {
        double accumulator = 0.0;
        
        for (size_t i = 0; i < 32; ++i) {
            double a_val = 0.0, b_val = 0.0;
            
            // 转换A数据
            switch (a_type) {
                case VDP_INT4: {
                    ac_int4_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT8: {
                    ac_int8_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT16: {
                    ac_int16_t val = a_data[i];
                    a_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_FP16: {
                    ac_float16_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_BF16: {
                    ac_bfloat16_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_FP8E4: {
                    ac_fp8e4_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
                case VDP_FP8E5: {
                    ac_fp8e5_t val;
                    val.set_data(a_data[i]);
                    a_val = val.to_double();
                    break;
                }
            }
            
            // 转换B数据
            switch (b_type) {
                case VDP_INT4: {
                    ac_int4_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT8: {
                    ac_int8_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_INT16: {
                    ac_int16_t val = b_data[i];
                    b_val = static_cast<double>(val.to_int());
                    break;
                }
                case VDP_FP16: {
                    ac_float16_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_BF16: {
                    ac_bfloat16_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_FP8E4: {
                    ac_fp8e4_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
                case VDP_FP8E5: {
                    ac_fp8e5_t val;
                    val.set_data(b_data[i]);
                    b_val = val.to_double();
                    break;
                }
            }
            
            accumulator += a_val * b_val;
        }
        
        return static_cast<float>(accumulator);
    }
    
    /**
     * @brief 比较两个结果是否在容差范围内
     */
    static bool compare_results(float computed, float reference, float tolerance) {
        if (std::isnan(computed) && std::isnan(reference)) return true;
        if (std::isinf(computed) && std::isinf(reference)) return true;
        if (computed == 0.0f && reference == 0.0f) return true;
        
        float abs_diff = std::abs(computed - reference);
        float rel_tolerance = tolerance * std::max(std::abs(computed), std::abs(reference));
        
        return abs_diff <= std::max(tolerance, rel_tolerance);
    }
    
    /**
     * @brief 获取数据类型名称
     */
    static std::string get_data_type_name(uint8_t data_type) {
        switch (data_type) {
            case VDP_INT4: return "INT4";
            case VDP_INT8: return "INT8";
            case VDP_INT16: return "INT16";
            case VDP_FP16: return "FP16";
            case VDP_BF16: return "BF16";
            case VDP_FP8E4: return "FP8E4";
            case VDP_FP8E5: return "FP8E5";
            default: return "UNKNOWN";
        }
    }
    
    /**
     * @brief 检查是否为浮点类型
     */
    static bool is_floating_point_type(uint8_t data_type) {
        return (data_type == VDP_FP16 || data_type == VDP_BF16 || 
                data_type == VDP_FP8E4 || data_type == VDP_FP8E5);
    }
    
    /**
     * @brief 获取适当的容差值
     */
    static float get_tolerance_for_types(uint8_t a_type, uint8_t b_type) {
        if (is_floating_point_type(a_type) || is_floating_point_type(b_type)) {
            return TOLERANCE_FLOATING;
        }
        return TOLERANCE_INTEGER;
    }
    
    /**
     * @brief 获取权重向量数量
     */
    static int get_weight_vector_count(uint8_t data_type) {
        switch (data_type) {
            case VDP_INT4: return 64;
            case VDP_INT8: return 32;
            case VDP_INT16: return 16;
            case VDP_FP16: return 16;
            case VDP_BF16: return 16;
            case VDP_FP8E4: return 32;
            case VDP_FP8E5: return 32;
            default: return 0;
        }
    }
    
    /**
     * @brief 转换为老版本数据类型枚举（用于C函数）
     */
    static uint8_t convert_to_c_enum(uint8_t vdp_type) {
        // VDP_xxx 到 C枚举的映射
        return vdp_type; // 假设枚举值相同
    }
};

/**
 * @brief 权重数据访问器（与原版保持一致）
 */
class WeightDataAccessor {
public:
    static bool get_weight_data(uint8_t weight_type, int weight_index, uint16_t* output_data) {
        switch (weight_type) {
            case VDP_INT4:
                if (weight_index >= 64) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int4[weight_index][i];
                }
                break;
            case VDP_INT8:
                if (weight_index >= 32) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int8[weight_index][i];
                }
                break;
            case VDP_INT16:
                if (weight_index >= 16) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_algn_int16[weight_index][i];
                }
                break;
            case VDP_FP16:
                if (weight_index >= 16) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp16[weight_index][i];
                }
                break;
            case VDP_BF16:
                if (weight_index >= 16) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_bf16[weight_index][i];
                }
                break;
            case VDP_FP8E4:
                if (weight_index >= 32) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp8e4[weight_index][i];
                }
                break;
            case VDP_FP8E5:
                if (weight_index >= 32) return false;
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = wt_mhex_fp8e5[weight_index][i];
                }
                break;
            default:
                return false;
        }
        return true;
    }
};

/**
 * @brief 输入数据访问器（与原版保持一致）
 */
class InputDataAccessor {
public:
    static bool get_input_data(uint8_t input_type, uint16_t* output_data) {
        switch (input_type) {
            case VDP_INT4:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int4[VDP_INT4][i];
                }
                break;
            case VDP_INT8:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int8[VDP_INT8][i];
                }
                break;
            case VDP_INT16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_int16[VDP_INT16][i];
                }
                break;
            case VDP_FP16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp16[VDP_FP16-1][i];
                }
                break;
            case VDP_BF16:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_bf16[VDP_BF16-1][i];
                }
                break;
            case VDP_FP8E4:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp8e4[VDP_FP8E4-1][i];
                }
                break;
            case VDP_FP8E5:
                for (int i = 0; i < 32; ++i) {
                    output_data[i] = in_table_tr_fp8e5[VDP_FP8E5-1][i];
                }
                break;
            default:
                return false;
        }
        return true;
    }
};

/**
 * @brief 三方法对比测试器
 */
class ThreeMethodComparisonTester {
private:
    std::vector<TestSummary> test_summaries;
    
public:
    /**
     * @brief 测试单个数据组合的三种方法
     */
    TripleResult test_single_combination(uint8_t input_type, uint8_t weight_type, int weight_index) {
        TripleResult result = {};
        
        // 准备数据缓冲区
        uint16_t input_data[64] = {0};
        uint16_t weight_data[64] = {0};
        uint16_t input_aligned[64] = {0};
        uint16_t weight_aligned[64] = {0};
        
        // 获取输入和权重数据
        if (!InputDataAccessor::get_input_data(input_type, input_data) ||
            !WeightDataAccessor::get_weight_data(weight_type, weight_index, weight_data)) {
            return result;
        }
        
        // 1. 计算参考结果（ac_datatypes）
        auto ref_start = std::chrono::high_resolution_clock::now();
        result.reference_result = ComparisonTestUtilities::calculate_reference_dot_product(
            input_data, input_type, weight_data, weight_type);
        auto ref_end = std::chrono::high_resolution_clock::now();
        result.reference_time_ms = std::chrono::duration<double, std::milli>(ref_end - ref_start).count();
        
        // 将参考结果转换为原始位表示
        memcpy(&result.reference_raw, &result.reference_result, sizeof(float));
        
        // 2. 计算C方法结果（dcim_macro_com）
        // 需要为浮点类型准备对齐数据
        auto c_start = std::chrono::high_resolution_clock::now();
        if (ComparisonTestUtilities::is_floating_point_type(input_type)) {
            float_data_align(input_data, ComparisonTestUtilities::convert_to_c_enum(input_type), input_aligned);
        } else {
            for (int i = 0; i < 32; ++i) {
                input_aligned[i] = input_data[i];
            }
            input_aligned[32] = 0;
        }
        
        if (ComparisonTestUtilities::is_floating_point_type(weight_type)) {
            float_data_align(weight_data, ComparisonTestUtilities::convert_to_c_enum(weight_type), weight_aligned);
        } else {
            for (int i = 0; i < 32; ++i) {
                weight_aligned[i] = weight_data[i];
            }
            weight_aligned[32] = 0;
        }
        
        result.c_raw = dcim_macro_com(weight_aligned, ComparisonTestUtilities::convert_to_c_enum(weight_type),
                                     input_aligned, ComparisonTestUtilities::convert_to_c_enum(input_type));
        auto c_end = std::chrono::high_resolution_clock::now();
        result.c_time_ms = std::chrono::duration<double, std::milli>(c_end - c_start).count();
        
        // 转换C结果为浮点数
        memcpy(&result.c_result, &result.c_raw, sizeof(float));
        
        // 3. 计算C++方法结果（VectorDotProductInterface）
        auto cpp_start = std::chrono::high_resolution_clock::now();
        result.cpp_raw = VectorDotProductInterface::compute_with_auto_prealign(
            input_data, input_type, weight_data, weight_type);
        auto cpp_end = std::chrono::high_resolution_clock::now();
        result.cpp_time_ms = std::chrono::duration<double, std::milli>(cpp_end - cpp_start).count();
        
        // 转换C++结果为浮点数
        memcpy(&result.cpp_result, &result.cpp_raw, sizeof(float));
        
        // 4. 计算误差和匹配状态
        float tolerance = ComparisonTestUtilities::get_tolerance_for_types(input_type, weight_type);
        
        result.c_vs_ref_error = std::abs(result.c_result - result.reference_result);
        result.cpp_vs_ref_error = std::abs(result.cpp_result - result.reference_result);
        result.c_vs_cpp_error = std::abs(result.c_result - result.cpp_result);
        
        result.c_matches_ref = ComparisonTestUtilities::compare_results(result.c_result, result.reference_result, tolerance);
        result.cpp_matches_ref = ComparisonTestUtilities::compare_results(result.cpp_result, result.reference_result, tolerance);
        result.c_matches_cpp = ComparisonTestUtilities::compare_results(result.c_result, result.cpp_result, tolerance);
        
        return result;
    }
    
    /**
     * @brief 测试特定类型组合的所有权重向量
     */
    void test_type_combination(uint8_t input_type, uint8_t weight_type) {
        std::string test_name = ComparisonTestUtilities::get_data_type_name(input_type) + " x " + 
                               ComparisonTestUtilities::get_data_type_name(weight_type);
        
        int weight_count = ComparisonTestUtilities::get_weight_vector_count(weight_type);
        
        std::cout << "\n=== 测试 " << test_name << " (" << weight_count << " 个权重向量) ===" << std::endl;
        
        TestSummary summary = {};
        summary.test_name = test_name;
        summary.total_tests = weight_count;
        
        double sum_c_ref_error = 0.0, sum_cpp_ref_error = 0.0, sum_c_cpp_error = 0.0;
        double sum_c_time = 0.0, sum_cpp_time = 0.0, sum_ref_time = 0.0;
        
        // 测试每个权重向量
        for (int weight_idx = 0; weight_idx < weight_count; ++weight_idx) {
            TripleResult result = test_single_combination(input_type, weight_type, weight_idx);
            
            // 更新匹配计数
            if (result.c_matches_ref) summary.c_ref_matches++;
            if (result.cpp_matches_ref) summary.cpp_ref_matches++;
            if (result.c_matches_cpp) summary.c_cpp_matches++;
            
            // 累计误差和时间
            sum_c_ref_error += result.c_vs_ref_error;
            sum_cpp_ref_error += result.cpp_vs_ref_error;
            sum_c_cpp_error += result.c_vs_cpp_error;
            sum_c_time += result.c_time_ms;
            sum_cpp_time += result.cpp_time_ms;
            sum_ref_time += result.reference_time_ms;
            
            // 更新最大误差
            summary.max_c_ref_error = std::max(summary.max_c_ref_error, (double)result.c_vs_ref_error);
            summary.max_cpp_ref_error = std::max(summary.max_cpp_ref_error, (double)result.cpp_vs_ref_error);
            summary.max_c_cpp_error = std::max(summary.max_c_cpp_error, (double)result.c_vs_cpp_error);
            
            // 仅显示C/C++结果不一致的详细信息
            if (!result.c_matches_cpp) {
                std::cout << std::fixed << std::setprecision(6);
                std::cout << "权重 #" << weight_idx << " (C/C++不一致):" << std::endl;
                std::cout << "  参考结果: " << result.reference_result 
                         << " (原始: 0x" << std::hex << result.reference_raw << std::dec << ")" << std::endl;
                std::cout << "  C 结果:   " << result.c_result 
                         << " (原始: 0x" << std::hex << result.c_raw << std::dec << ")" << std::endl;
                std::cout << "  C++结果:  " << result.cpp_result 
                         << " (原始: 0x" << std::hex << result.cpp_raw << std::dec << ")" << std::endl;
                std::cout << "  误差 C vs 参考:   " << result.c_vs_ref_error 
                         << (result.c_matches_ref ? " ✓" : " ✗") << std::endl;
                std::cout << "  误差 C++ vs 参考: " << result.cpp_vs_ref_error 
                         << (result.cpp_matches_ref ? " ✓" : " ✗") << std::endl;
                std::cout << "  误差 C vs C++:    " << result.c_vs_cpp_error 
                         << (result.c_matches_cpp ? " ✓" : " ✗") << std::endl;
                std::cout << "  时间 (参考/C/C++): " << result.reference_time_ms << "ms / " 
                         << result.c_time_ms << "ms / " << result.cpp_time_ms << "ms" << std::endl;
                std::cout << std::endl;
            }
        }
        
        // 计算平均值
        summary.avg_c_ref_error = sum_c_ref_error / weight_count;
        summary.avg_cpp_ref_error = sum_cpp_ref_error / weight_count;
        summary.avg_c_cpp_error = sum_c_cpp_error / weight_count;
        summary.avg_c_time_ms = sum_c_time / weight_count;
        summary.avg_cpp_time_ms = sum_cpp_time / weight_count;
        summary.avg_ref_time_ms = sum_ref_time / weight_count;
        
        // 显示汇总
        std::cout << "\n" << test_name << " 汇总:" << std::endl;
        std::cout << "  匹配率 (C vs 参考):   " << summary.c_ref_matches << "/" << weight_count 
                 << " (" << std::fixed << std::setprecision(1) 
                 << (100.0 * summary.c_ref_matches / weight_count) << "%)" << std::endl;
        std::cout << "  匹配率 (C++ vs 参考): " << summary.cpp_ref_matches << "/" << weight_count 
                 << " (" << (100.0 * summary.cpp_ref_matches / weight_count) << "%)" << std::endl;
        std::cout << "  匹配率 (C vs C++):    " << summary.c_cpp_matches << "/" << weight_count 
                 << " (" << (100.0 * summary.c_cpp_matches / weight_count) << "%)" << std::endl;
        std::cout << std::scientific << std::setprecision(3);
        std::cout << "  平均误差 (C vs 参考):   " << summary.avg_c_ref_error << std::endl;
        std::cout << "  平均误差 (C++ vs 参考): " << summary.avg_cpp_ref_error << std::endl;
        std::cout << "  平均误差 (C vs C++):    " << summary.avg_c_cpp_error << std::endl;
        std::cout << "  最大误差 (C vs 参考):   " << summary.max_c_ref_error << std::endl;
        std::cout << "  最大误差 (C++ vs 参考): " << summary.max_cpp_ref_error << std::endl;
        std::cout << "  最大误差 (C vs C++):    " << summary.max_c_cpp_error << std::endl;
        std::cout << std::fixed << std::setprecision(4);
        std::cout << "  平均时间 (参考):        " << summary.avg_ref_time_ms << "ms" << std::endl;
        std::cout << "  平均时间 (C):           " << summary.avg_c_time_ms << "ms" << std::endl;
        std::cout << "  平均时间 (C++):         " << summary.avg_cpp_time_ms << "ms" << std::endl;
        
        test_summaries.push_back(summary);
    }
    
    /**
     * @brief 运行全面的三方法对比测试
     */
    void run_comprehensive_comparison() {
        std::cout << "=== 三种计算方法综合对比测试 ===" << std::endl;
        std::cout << "方法1: reference - ac_datatypes (黄金标准)" << std::endl;
        std::cout << "方法2: c_cal - dcim_macro_com (C语言实现)" << std::endl;
        std::cout << "方法3: cpp_cal - VectorDotProductInterface (C++实现)" << std::endl;
        std::cout << "支持数据类型: INT4, INT8, INT16, FP16, BF16, FP8E4, FP8E5" << std::endl;
        
        std::vector<uint8_t> all_types = {VDP_INT4, VDP_INT8, VDP_INT16, VDP_FP16, VDP_BF16, VDP_FP8E4, VDP_FP8E5};
        
        auto overall_start = std::chrono::high_resolution_clock::now();
        
        // 测试所有组合
        for (uint8_t input_type : all_types) {
            for (uint8_t weight_type : all_types) {
                test_type_combination(input_type, weight_type);
            }
        }
        
        auto overall_end = std::chrono::high_resolution_clock::now();
        double total_time = std::chrono::duration<double, std::milli>(overall_end - overall_start).count();
        
        // 生成最终统计报告
        generate_final_report(total_time);
    }
    
private:
    /**
     * @brief 生成最终统计报告
     */
    void generate_final_report(double total_time_ms) {
        std::cout << "\n\n=== 最终统计报告 ===" << std::endl;
        
        int total_tests = 0;
        int total_c_ref_matches = 0;
        int total_cpp_ref_matches = 0;
        int total_c_cpp_matches = 0;
        double total_c_time = 0.0;
        double total_cpp_time = 0.0;
        double total_ref_time = 0.0;
        
        std::cout << "\n按类型组合统计:" << std::endl;
        std::cout << std::setw(20) << std::left << "类型组合" 
                 << std::setw(8) << std::right << "测试数" 
                 << std::setw(11) << "C匹配率%" 
                 << std::setw(12) << "C++匹配率%" 
                 << std::setw(12) << "C⬌C++匹配%" 
                 << std::setw(11) << "C时间(ms)" 
                 << std::setw(12) << "C++时间(ms)" << std::endl;
        std::cout << std::string(96, '-') << std::endl;
        
        for (const auto& summary : test_summaries) {
            total_tests += summary.total_tests;
            total_c_ref_matches += summary.c_ref_matches;
            total_cpp_ref_matches += summary.cpp_ref_matches;
            total_c_cpp_matches += summary.c_cpp_matches;
            total_c_time += summary.avg_c_time_ms * summary.total_tests;
            total_cpp_time += summary.avg_cpp_time_ms * summary.total_tests;
            total_ref_time += summary.avg_ref_time_ms * summary.total_tests;
            
            std::cout << std::setw(20) << std::left << summary.test_name
                     << std::setw(8) << std::right << summary.total_tests
                     << std::setw(10) << std::fixed << std::setprecision(1) 
                     << (100.0 * summary.c_ref_matches / summary.total_tests) << "%"
                     << std::setw(11) << (100.0 * summary.cpp_ref_matches / summary.total_tests) << "%"
                     << std::setw(11) << (100.0 * summary.c_cpp_matches / summary.total_tests) << "%"
                     << std::setw(10) << std::setprecision(4) << summary.avg_c_time_ms
                     << std::setw(12) << summary.avg_cpp_time_ms << std::endl;
        }
        
        std::cout << std::string(96, '-') << std::endl;
        std::cout << "\n总体统计:" << std::endl;
        std::cout << "总测试数: " << total_tests << std::endl;
        std::cout << "C vs 参考匹配率: " << total_c_ref_matches << "/" << total_tests 
                 << " (" << std::fixed << std::setprecision(2) 
                 << (100.0 * total_c_ref_matches / total_tests) << "%)" << std::endl;
        std::cout << "C++ vs 参考匹配率: " << total_cpp_ref_matches << "/" << total_tests 
                 << " (" << (100.0 * total_cpp_ref_matches / total_tests) << "%)" << std::endl;
        std::cout << "C vs C++匹配率: " << total_c_cpp_matches << "/" << total_tests 
                 << " (" << (100.0 * total_c_cpp_matches / total_tests) << "%)" << std::endl;
        
        std::cout << "\n性能对比:" << std::endl;
        std::cout << "平均执行时间 (参考): " << std::setprecision(4) << (total_ref_time / total_tests) << "ms" << std::endl;
        std::cout << "平均执行时间 (C):    " << (total_c_time / total_tests) << "ms" << std::endl;
        std::cout << "平均执行时间 (C++):  " << (total_cpp_time / total_tests) << "ms" << std::endl;
        std::cout << "C++ vs C 速度比:     " << std::setprecision(2) 
                 << ((total_c_time / total_tests) / (total_cpp_time / total_tests)) << "x" << std::endl;
        
        std::cout << "\n总测试时间: " << std::setprecision(2) << (total_time_ms / 1000.0) << " 秒" << std::endl;
        
        // 一致性分析
        if (total_c_ref_matches == total_tests && total_cpp_ref_matches == total_tests) {
            std::cout << "\n✅ 所有方法都与参考实现完全一致!" << std::endl;
        } else {
            std::cout << "\n⚠️  发现不一致情况，需要进一步分析:" << std::endl;
            if (total_c_ref_matches < total_tests) {
                std::cout << "  - C方法有 " << (total_tests - total_c_ref_matches) << " 个不匹配案例" << std::endl;
            }
            if (total_cpp_ref_matches < total_tests) {
                std::cout << "  - C++方法有 " << (total_tests - total_cpp_ref_matches) << " 个不匹配案例" << std::endl;
            }
        }
    }
};

int main() {
    std::cout << "三种计算方法对比测试系统" << std::endl;
    std::cout << "=================================" << std::endl;
    
    try {
        ThreeMethodComparisonTester tester;
        tester.run_comprehensive_comparison();
        
        std::cout << "\n测试完成!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败，异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试失败，未知异常" << std::endl;
        return 1;
    }
} 